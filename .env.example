# PostgreSQL Database Connection (replace username, password, host, port, and dbname)
DATABASE_URL='postgresql://username:password@localhost:5432/superexpert_ai_db'

# LLM Provider API Keys (register accounts to obtain your keys)
OPENAI_API_KEY='<Your OpenAI API Key>'
GEMINI_API_KEY='<Your Gemini API Key>'
ANTHROPIC_API_KEY='<Your Anthropic API Key>'

# NextAuth.js v5 Authentication
NEXTAUTH_URL='http://localhost:3000'
NEXTAUTH_SECRET='<Your Generated Secret>' # Generate with openssl rand -base64 32
version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    container_name: superexpert-postgres
    environment:
      POSTGRES_DB: superexpert_ai_db
      POSTGRES_USER: superexpert
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped

volumes:
  postgres_data:

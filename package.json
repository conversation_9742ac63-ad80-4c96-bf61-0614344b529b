{"name": "superexpert-ai", "version": "1.0.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--disable-warning=DEP0040' next dev --turbopack", "dev:pretty": "NODE_OPTIONS='--disable-warning=DEP0040' npm run dev | npx pino-pretty --colorize --translateTime 'HH:MM:ss' --expand --indent 2", "build": "next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "migrate": "npx prisma generate && npx prisma db push", "migrate:production": "dotenv -e .env.production -- npx prisma db push", "nuke-db": "npx prisma db push --force-reset", "nuke-db:production": "dotenv -e .env.production npx prisma migrate reset --force && dotenv -e .env.production npx prisma db push", "trim-demo-agents": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/trim-demo-agents.ts", "test": "NODE_OPTIONS='--disable-warning=DEP0040' jest", "test-all": "NODE_OPTIONS='--disable-warning=DEP0040' jest --testPathIgnorePatterns=", "test-current": "NODE_OPTIONS='--disable-warning=DEP0040' jest __tests__/known-issues/gemini.test.ts --testPathIgnorePatterns="}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^3.10.0", "@superexpert-ai/framework": "^1.0.27", "@typescript-eslint/parser": "^8.23.0", "bcryptjs": "^2.4.3", "bottleneck": "^2.19.5", "comment-parser": "^1.4.1", "dotenv": "^16.4.7", "fs": "^0.0.1-security", "js-tiktoken": "^1.0.19", "next": "^15.1.7", "next-auth": "^5.0.0-beta.25", "openai": "^4.86.1", "path": "^0.12.7", "pg-tsquery": "^8.4.2", "pgvector": "^0.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "reflect-metadata": "^0.2.2", "stopword": "^3.1.4", "tiktoken": "^1.0.20", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.22.0", "@prisma/client": "^6.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "20.19.6", "@types/react": "19.1.8", "@types/react-dom": "^19", "@types/stopword": "^2.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.22.0", "eslint-config-next": "15.1.6", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "pino-pretty": "^13.0.0", "postcss": "^8", "prisma": "^6.7.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "5.8.3", "typescript-eslint": "^8.26.1"}}
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

model taskDefinitions {
  id                        String   @id @default(uuid())
  userId                    String
  agentId                   String
  isSystem                  Boolean  @default(false)
  name                      String
  description               String
  instructions              String
  startNewThread            Boolean  @default(false)
  corpusLimit               Int      @default(3)
  corpusSimilarityThreshold Int      @default(50)
  corpusIds                 String[]
  contextToolIds            String[]
  ragStrategyId             String   @default("global")
  serverToolIds             String[]
  clientToolIds             String[]
  modelId                   String
  maximumOutputTokens       Int?
  temperature               Float?
  theme                     String   @default("global")
  createdAt                 DateTime @default(now())

  @@unique([agentId, name], name: "taskDefinition_agentId_name") // Composite unique constraint
  @@map("superexpert_ai_taskDefinitions")
}

model messages {
  id           Int      @id @default(autoincrement())
  agentId      String
  userId       String
  task         String
  thread       String
  role         String
  content      String
  tool_calls   Json?
  tool_call_id String?
  createdAt    DateTime @default(now())

  @@map("superexpert_ai_messages")
}

model memories {
  id            Int      @id @default(autoincrement())
  userId        String
  agentId       String
  content       String
  expiresInDays Int
  createdAt     DateTime @default(now())

  @@map("superexpert_ai_memories")
}

model users {
  id       String @id @default(uuid())
  email    String @unique
  password String

  @@map("superexpert_ai_users")
}

model profiles {
  id      String @id @default(uuid())
  userId  String
  agentId String
  name    String
  value   String

  @@unique([userId, agentId, name], name: "userId_agentId_name") // Composite unique constraint
  @@map("superexpert_ai_profiles")
}

model agents {
  id          String   @id @default(uuid())
  userId      String
  name        String   @unique
  description String
  createdAt   DateTime @default(now())

  @@unique([userId, name], name: "agent_userId_name") // Composite unique constraint
  @@map("superexpert_ai_agents")
}

model consents {
  id        String   @id @default(uuid())
  userId    String
  type      String
  createdAt DateTime @default(now())

  @@map("superexpert_ai_consents")
}

model corpus {
  id          String   @id @default(uuid())
  userId      String
  name        String
  description String
  createdAt   DateTime @default(now())

  /// One-to-many: a corpus owns many files
  corpusFiles corpusFiles[] @relation("CorpusToFiles")

  @@unique([userId, name])
  @@map("superexpert_ai_corpus")
}

/// ──────────────────────────────────────────────────────────
/// 2)  corpusFiles  – one row per uploaded file
model corpusFiles {
  id           String   @id @default(uuid())
  userId       String
  fileName     String
  chunkSize    Int
  chunkOverlap Int
  done          Boolean  @default(false)
  createdAt    DateTime @default(now())

  /// FK → corpus
  corpusId String
  corpus   corpus @relation("CorpusToFiles", fields: [corpusId], references: [id])

  /// One-to-many: file → chunks
  corporaFileChunks corpusFileChunks[] @relation("FilesToChunks")

  /// One-to-one: file → progress  (parent side has NO fields/references)
  corpusFileProgress corpusFileProgress? @relation("FileToProgress")

  @@unique([corpusId, fileName])
  @@map("superexpert_ai_corpusFiles")
}

/// ──────────────────────────────────────────────────────────
/// 3)  corpusFileChunks  – individual text/embedding chunks
model corpusFileChunks {
  id         Int                          @id @default(autoincrement())
  userId     String
  chunkIndex Int
  chunk      String
  chunkTSV  Unsupported("TSVECTOR")?
  embedding  Unsupported("vector(1536)")?

  /// FK → corpusFiles   (child side has fields/references + cascade)
  corpusFileId String
  corpusFile   corpusFiles @relation("FilesToChunks", fields: [corpusFileId], references: [id], onDelete: Cascade)

  /// prevents duplicate inserts when resuming
  @@unique([corpusFileId, chunkIndex])
  @@map("superexpert_ai_corpusFileChunks")
}


model corpusTermFrequencies {
  corpusId String @map("corpusId") // FK
  lexeme   String
  ndoc     Int       // number of chunks that contain the lexeme
  nentry   Int       // total occurrences (optional — for TF-IDF)

  @@id([corpusId, lexeme])                    // composite PK
  @@map("superexpert_ai_corpusTermFrequencies")
}

/// ──────────────────────────────────────────────────────────
/// 4)  corpusFileProgress  – checkpoint for resumable ingest
model corpusFileProgress {
  corpusFileId String   @id // PK = FK
  lastChunk    Int      @default(-1)
  updatedAt    DateTime @updatedAt @default(now())

  /// FK → corpusFiles  (child side owns fields/references + cascade)
  corpusFile corpusFiles @relation("FileToProgress", fields: [corpusFileId], references: [id], onDelete: Cascade)

  @@map("superexpert_ai_corpusFileProgress")
}

model attachments {
  id               String   @id @default(uuid())
  userId           String
  taskDefinitionId String
  fileName         String
  createdAt        DateTime @default(now())
  file             String   @db.Text

  @@map("superexpert_ai_attachments")
}


model LogEvents {
  id        BigInt   @id @default(autoincrement()) @db.BigInt
  createdAt DateTime @default(now())               @db.Timestamptz(3)

  userId    String?  @db.VarChar(36)
  agentId   String?  @db.VarChar(36)
  component String?  @db.VarChar(50)
  level     String   @db.VarChar(10)
  msg       String

  data      Json?

  @@index([agentId, createdAt])
  @@map("superexpert_ai_logEvents")
}




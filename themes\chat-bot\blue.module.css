@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

.chatPage {
    @apply flex flex-col min-h-screen;
    font-family: 'Inter', sans-serif;
}

.header {
    @apply w-full bg-white shadow;
}

.headerInner {
    @apply w-full px-6 sm:px-8 lg:px-10 mx-auto;
}

.footer {
    @apply text-center py-3 bg-white text-base text-gray-500 shadow-sm;
}

.headerLeft {
    @apply flex items-center h-14 space-x-4;
}

.logo {
    @apply text-2xl font-bold text-gray-900;
}

.logoIcon {
    @apply bg-black text-white rounded-full w-8 h-8 inline-flex items-center justify-center;
}
.signOutButton {
    @apply text-sm text-gray-700 hover:text-gray-900;
}

.main {
    @apply flex-1 flex flex-col bg-gray-50;
}

.chatContainer {
    @apply flex flex-col flex-1 relative;
}

.contentWrapper {
    @apply flex flex-col flex-1 justify-between absolute inset-0 p-4;
}

.messages {
    @apply flex flex-col gap-2 overflow-y-auto mb-4;
}

.inputForm {
    @apply flex items-center gap-2 w-full;
}

.userMessage,
.assistantMessage {
    @apply rounded-xl px-5 py-3 shadow-sm border text-base;
    overflow-wrap: break-word;
    max-width: 80%;
}

.userMessage {
    @apply self-end bg-blue-600 text-white border-blue-700;
}

.assistantMessage {
    @apply self-start bg-gray-100 text-gray-800 border-gray-200;
}

.inputForm {
    @apply flex items-center bg-white p-2 shadow-md sticky bottom-0 mx-auto w-full;
    max-width: 100%;
}

.input {
    @apply flex-1 border border-gray-300 rounded-full px-5 py-3
           text-gray-700 placeholder-gray-400 text-base
           focus:outline-none focus:ring-2 focus:ring-blue-500
           focus:border-transparent shadow-sm;
}

.button {
    @apply ml-2 px-6 py-3 bg-blue-700 text-white rounded-full font-medium text-base
           hover:bg-blue-800 transition-colors duration-200 ease-in-out
           disabled:opacity-50 disabled:cursor-not-allowed shadow-sm;
}


.busyWaitContainer {
    @apply flex justify-center items-center my-6;
}

.busyWait {
    @apply h-6 w-6 animate-spin rounded-full border-4 border-blue-500 border-t-transparent;
}

/* Modal overlay: semi-transparent background */
.modalOverlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50;
}

/* Modal content: center-aligned box */
.modalContent {
    @apply bg-white rounded-lg shadow-lg p-6 w-full max-w-md relative;
}




